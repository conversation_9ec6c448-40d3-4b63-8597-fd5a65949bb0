let plzData = [];

document.addEventListener('DOMContentLoaded', async () => {
  try {
    const res = await fetch('../data/plz.json');
    plzData = await res.json();
  } catch (err) {
    console.error('Fehler beim Laden der PLZ-Daten:', err);
  }
});

document.querySelector('#submit').addEventListener('click', () => {
  const userPlz = document.querySelector('#plz').value.trim();

  if (!userPlz) return;

  const match = plzData.find((entry) =>
    entry.plz.map((p) => p.trim()).includes(userPlz)
  );

  window.location.href = match
    ? match.redirect
    : './not-eligible.html';
});

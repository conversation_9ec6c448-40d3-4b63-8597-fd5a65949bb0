@import url('fonts.css');

:root {
  /* colors */
  --primary-color-03-synergy-green-new: #587d00;
  --secondary-background-color: linear-gradient(
    180deg,
    #890067 0%,
    #50003c 100%
  );
  --secondary-background-image: linear-gradient(
    180deg,
    color(display-p3 0.4902 0.0784 0.3922) 0%,
    color(display-p3 0.2863 0.0314 0.2275) 100%
  );
  --body-text-dark: #575756;
  --body-text-white: #fff;
  --body-background-color: #f1f1ee;

  --input-border-color: #890067;

  --button-text-color: #f1f1ee;
  --button-background-color: var(--secondary-color);
  --button-background-image: var(--secondary-background-image);
  --button-flat-color: #890067;
  --button-accent-color: #49083a;

  /* typo */
  --emergy-font-family: 'Ropa Sans Pro', sans-serif;
}

body {
  font-family: var(--emergy-font-family);
  font-weight: 400;
  margin: 0;
  color: var(--body-text-dark);
  background-color: var(--body-background-color);
  max-width: 100%;
}

h1 {
  color: var(--primary-color-03-synergy-green-new);
  font-family: var(--emergy-font-family);
  font-size: clamp(2rem, 6vw, 80px);
  font-style: normal;
  font-weight: 700;
  line-height: 1.1;
  margin: 0;
}

p {
  font-family: var(--emergy-font-family);
  font-size: clamp(1rem, 2.5vw, 18px);
  font-style: normal;
  font-weight: 400;
  line-height: 1.4;
  color: var(--body-text-dark);
}

.link {
  color: var(--input-border-color);
  text-decoration: none;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}

.link__container {
  display: flex;
  position: absolute;
  flex-direction: row;
  gap: 1.5rem;
  bottom: 2%;
}

.link--backward {
  display: flex;
  flex-direction: row;
  gap: 2px;
  font-weight: 700;
  transition: gap 0.2s;
}

.link--backward:hover {
  gap: 8px;
}

.link--backward:focus-visible {
  text-decoration: underline var(--input-border-color);
  text-decoration-thickness: 2px;
  outline: none;
}

.button-primary {
  font-family: var(--emergy-font-family);
  font-size: 18px;
  font-weight: 700;
  line-height: 22px;
  display: flex;
  height: auto;
  padding: 13px 24px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 32px;
  color: var(--button-text-color);
  background-color: var(--button-background-color);
  background-image: var(--button-background-image);
  cursor: pointer;
  text-decoration: none;
}

.button-primary:focus-visible,
.button-primary:hover,
.button-primary:focus {
  background-image: none;
  background-color: var(--button-accent-color);
}

.button-primary:focus-visible {
  outline: 0.125rem solid var(--button-accent-color);
  outline-offset: 0.125rem;
  border-radius: 50rem;
}

form {
  width: 100%;
  max-width: 300px;
  margin-bottom: 1rem;
}

input {
  width: 100%;
  max-width: 300px;
  height: 64px;
  padding-left: 1rem;
  font-family: var(--emergy-font-family);
  font-size: 18px;
  font-weight: 400;
  color: var(--body-text-dark);
  border: 2px solid var(--input-border-color);
}

input::placeholder {
  font-family: var(--emergy-font-family);
  font-size: 18px;
  font-weight: 400;
  color: var(--body-text-dark);
}

input:focus-visible {
  outline: 0.125rem solid var(--button-accent-color);
  /* outline-offset: 0.125rem; */
}

label {
  display: inline-flex;
  height: 2rem;
  padding: 4px 16px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 2px 2px 0px 0px;
  color: var(--body-text-white);
  background-color: var(--secondary-background-color);
  background-image: var(--secondary-background-image);
}

.page-wrapper {
  width: 100%;
  max-width: 1920px;
  margin: 0 auto;
}

.split-stage {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100vh;
}

.split-stage__item--image {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-image: url('../assets/images/336caf1a0c95598b00a3d1980db566429a031901.jpg');
}

@media (min-width: 1000px) and (max-width: 1024px)  {
  .split-stage__item--image {
    background-position: 70% center;
  }
}

.split-stage__item--imageExit {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-image: url('../assets/images/2bdc3f3709760d3e7fa6ac9fc3e86949a1f8673d.jpg');
}

.split-stage__item--content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.content-wrapper {
  width: 100%;
  max-width: 700px;
  padding: 1rem;
}

.content-inner {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  max-width: 566px;
  justify-content: center;
  align-items: flex-start;
}

small {
  margin-bottom: 1rem;
}

.split-stage__container {
  max-width: 960px;
  padding: 0 2rem;
  margin: 0 0 1rem 0;
}

.split-stage__item {
  flex: 1 1 50%;
  position: relative;
}

@media (max-width: 767px) {
  .content-wrapper {
    padding: 0;
  }

  input {
    width: 90%;
  }
}
@media (min-width: 769px) {
  .split-stage {
    flex-direction: row;
  }
}

@media (max-width: 769px) {
  .split-stage__item {
    min-height: 0px;
  }

  .split-stage__item--image {
    min-height: 675px;
    background-position: 75% top;
  }

  .split-stage__item--imageExit {
    min-height: 675px;
    background-position: top;
  }

  .split-stage__item--content {
    min-height: auto;
  }

  .split-stage__container {
    padding: 0;
    margin: 0 0 2rem 0;
  }

  .content-inner {
    gap: 4px;
  }

  .link--backward {
    margin-bottom: 1rem;
  }
}

@media (min-width: 992px) {
  .split-stage__item::after {
    content: '';
    display: block;
    height: 100%;
    width: 12%;
    position: absolute;
    right: 100%;
    /* top: 0; */
    background-image: url(data:image/svg+xml;base64,PHN2ZyB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjAiIHk9IjAiIHdpZHRoPSI1MCIgaGVpZ2h0PSI1NDAiIHZpZXdCb3g9IjAsIDAsIDUwLCA1NDAiPgogIDxnIGlkPSJFYmVuZV8xIj4KICAgIDxwYXRoIGQ9Ik01MCwwIEw1MCw1NDAgTDAsNTQwIEMwLDU0MCAxMDcuNTY1LDI3NyAwLDAgTDUwLDAgeiIgZmlsbD0iI0YxRjFFRSIvPgogIDwvZz4KPC9zdmc+Cg==);
    background-repeat: no-repeat;
    background-size: cover;
  }
}

@media (min-width: 1900px) {
  .split-stage__item::after {
    width: 11%;
  }
}
@media (max-width: 1898px) {
  .split-stage__item::after {
    width: 9%;
  }
}
@media (max-width: 1500px) {
  .split-stage__item::after {
    width: 11%;
  }
}
@media (max-width: 1200px) {
  .split-stage__item::after {
    width: 13%;
  }
}
@media (max-width: 1025px) {
  .split-stage__item::after {
    width: 25%;
  }
}
@media (max-width: 991px) {
  .split-stage__item::after {
    width: 25%;
  }
}


